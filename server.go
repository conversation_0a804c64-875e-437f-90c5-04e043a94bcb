package main

import (
	"context"
	"errors"
	"fmt"
	"sync"

	"github.com/BhaumikTalwar/Amrita/models/dto"
	gameapi "github.com/BhaumikTalwar/Amrita/service/GameApi"
)

type PlayerState int

const (
	PlayerStateActive PlayerState = iota
	PlayerStateLeft
)

type PlayerInfo struct {
	Username     string
	SessionToken string
	Symbol       string
	State        PlayerState
}

type DotsAndBoxes struct{}

func NewGame() gameapi.Game {
	return &DotsAndBoxes{}
}

type DotsAndBoxesInstance struct {
	roomID      string
	gridSize    int
	lines       map[string]string // lineID -> playerID who drew it
	boxes       map[string]string // boxID -> playerID who completed it
	players     map[string]string // playerID -> symbol (1 or 2) - kept for backward compatibility
	playerInfo  map[string]*PlayerInfo // playerID -> PlayerInfo
	currentTurn string            // "1" or "2"
	scores      map[string]int    // symbol -> score
	config      gameapi.GameConfig
	mu          sync.Mutex
	isGameOver  bool
	winner      string
}

func (g *DotsAndBoxes) NewInstance(config gameapi.GameConfig, roomID string) gameapi.GameInstance {
	if err := g.ValidateConfig(config); err != nil {
		return nil
	}
	
	gridSize := 8 // Default grid size
	if size, ok := config["gridSize"]; ok {
		if s, ok := size.(int); ok {
			gridSize = s
		}
	}
	
	return &DotsAndBoxesInstance{
		roomID:      roomID,
		gridSize:    gridSize,
		lines:       make(map[string]string),
		boxes:       make(map[string]string),
		players:     make(map[string]string),
		playerInfo:  make(map[string]*PlayerInfo),
		scores:      map[string]int{"1": 0, "2": 0},
		config:      config,
		isGameOver:  false,
	}
}

func (g *DotsAndBoxes) ValidateConfig(config gameapi.GameConfig) error {
	if size, ok := config["gridSize"]; ok {
		if s, ok := size.(int); !ok || s < 4 || s > 10 {
			return errors.New("gridSize must be an integer between 4 and 10")
		}
	}
	return nil
}

func (g *DotsAndBoxesInstance) HandlePlayerJoin(ctx context.Context, playerID string, sessionId string, playerData interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if len(g.playerInfo) >= 2 {
		return errors.New("game is full")
	}

	pData, ok := playerData.(map[string]interface{})
	if !ok {
		return errors.New("invalid player data format")
	}
	name, ok := pData["username"].(string)
	if !ok {
		return errors.New("username not provided")
	}

	symbol := "1"
	if len(g.playerInfo) == 1 {
		symbol = "2"
	}

	// Create PlayerInfo
	g.playerInfo[playerID] = &PlayerInfo{
		Username:     name,
		SessionToken: sessionId,
		Symbol:       symbol,
		State:        PlayerStateActive,
	}

	// Keep backward compatibility
	g.players[playerID] = symbol

	if len(g.playerInfo) == 2 {
		g.currentTurn = "1"
	}

	return nil
}

func (g *DotsAndBoxesInstance) HandlePlayerAction(ctx context.Context, playerID string, action interface{}) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	if g.isGameOver {
		return errors.New("game is over")
	}

	symbol, exists := g.players[playerID]
	if !exists {
		return errors.New("player not in game")
	}
	if symbol != g.currentTurn {
		return errors.New("not your turn")
	}

	move, ok := action.(map[string]interface{})
	if !ok {
		return errors.New("invalid action format")
	}

	startRow, ok := move["startRow"].(float64)
	if !ok {
		return errors.New("invalid startRow")
	}
	startCol, ok := move["startCol"].(float64)
	if !ok {
		return errors.New("invalid startCol")
	}
	endRow, ok := move["endRow"].(float64)
	if !ok {
		return errors.New("invalid endRow")
	}
	endCol, ok := move["endCol"].(float64)
	if !ok {
		return errors.New("invalid endCol")
	}

	sr, sc, er, ec := int(startRow), int(startCol), int(endRow), int(endCol)
	
	// Validate move bounds
	if sr < 0 || sr >= g.gridSize || sc < 0 || sc >= g.gridSize ||
	   er < 0 || er >= g.gridSize || ec < 0 || ec >= g.gridSize {
		return errors.New("move out of bounds")
	}

	// Validate that it's a valid line (adjacent dots)
	if !g.isValidLine(sr, sc, er, ec) {
		return errors.New("invalid line connection")
	}

	lineID := g.getLineID(sr, sc, er, ec)
	if _, exists := g.lines[lineID]; exists {
		return errors.New("line already exists")
	}

	// Draw the line
	g.lines[lineID] = symbol

	// Check for completed boxes
	completedBoxes := g.checkCompletedBoxes(lineID)
	
	if len(completedBoxes) > 0 {
		// Player gets points and another turn
		g.scores[symbol] += len(completedBoxes)
		for _, boxID := range completedBoxes {
			g.boxes[boxID] = symbol
		}
		// Current player keeps their turn
	} else {
		// Switch turns
		if g.currentTurn == "1" {
			g.currentTurn = "2"
		} else {
			g.currentTurn = "1"
		}
	}

	// Check if game is over
	totalBoxes := (g.gridSize - 1) * (g.gridSize - 1)
	if len(g.boxes) == totalBoxes {
		g.isGameOver = true
		if g.scores["1"] > g.scores["2"] {
			g.winner = "1"
		} else if g.scores["2"] > g.scores["1"] {
			g.winner = "2"
		}
		// If scores are equal, winner remains empty (draw)
	}

	return nil
}

func (g *DotsAndBoxesInstance) isValidLine(sr, sc, er, ec int) bool {
	rowDiff := abs(sr - er)
	colDiff := abs(sc - ec)
	
	// Must be adjacent (horizontally or vertically, not diagonally)
	return (rowDiff == 1 && colDiff == 0) || (rowDiff == 0 && colDiff == 1)
}

func (g *DotsAndBoxesInstance) getLineID(sr, sc, er, ec int) string {
	// Normalize line ID to ensure consistency
	minRow, maxRow := min(sr, er), max(sr, er)
	minCol, maxCol := min(sc, ec), max(sc, ec)
	
	if minRow == maxRow {
		// Horizontal line
		return fmt.Sprintf("h-%d-%d", minRow, minCol)
	} else {
		// Vertical line
		return fmt.Sprintf("v-%d-%d", minRow, minCol)
	}
}

func (g *DotsAndBoxesInstance) checkCompletedBoxes(newLineID string) []string {
	var completedBoxes []string
	
	// Check all possible boxes that could be completed by this line
	for row := 0; row < g.gridSize-1; row++ {
		for col := 0; col < g.gridSize-1; col++ {
			boxID := fmt.Sprintf("box-%d-%d", row, col)
			
			// Skip if box is already completed
			if _, exists := g.boxes[boxID]; exists {
				continue
			}
			
			// Check if all four sides of the box are drawn
			top := fmt.Sprintf("h-%d-%d", row, col)
			bottom := fmt.Sprintf("h-%d-%d", row+1, col)
			left := fmt.Sprintf("v-%d-%d", row, col)
			right := fmt.Sprintf("v-%d-%d", row, col+1)
			
			if g.hasLine(top) && g.hasLine(bottom) && g.hasLine(left) && g.hasLine(right) {
				completedBoxes = append(completedBoxes, boxID)
			}
		}
	}
	
	return completedBoxes
}

func (g *DotsAndBoxesInstance) hasLine(lineID string) bool {
	_, exists := g.lines[lineID]
	return exists
}

func abs(x int) int {
	if x < 0 {
		return -x
	}
	return x
}

func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

func max(a, b int) int {
	if a > b {
		return a
	}
	return b
}

func (g *DotsAndBoxesInstance) HandlePlayerLeave(ctx context.Context, playerID string) error {
	g.mu.Lock()
	defer g.mu.Unlock()

	leaverInfo, exists := g.playerInfo[playerID]
	if !exists || leaverInfo.State == PlayerStateLeft {
		return nil
	}

	g.isGameOver = true
	leaverInfo.State = PlayerStateLeft

	// The remaining player wins
	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			g.winner = info.Symbol
			break
		}
	}

	return nil
}

func (g *DotsAndBoxesInstance) GetGameState(playerID string) interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	symbol, exists := g.players[playerID]
	if !exists {
		return map[string]interface{}{
			"error": "player not in game",
		}
	}

	return map[string]interface{}{
		"gridSize":    g.gridSize,
		"lines":       g.lines,
		"boxes":       g.boxes,
		"symbol":      symbol,
		"currentTurn": g.currentTurn,
		"players":     g.buildPlayerNameMap(),
		"scores":      g.scores,
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *DotsAndBoxesInstance) GetPublicState() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()

	return map[string]interface{}{
		"gridSize":    g.gridSize,
		"lines":       g.lines,
		"boxes":       g.boxes,
		"currentTurn": g.currentTurn,
		"players":     g.buildPlayerNameMap(),
		"scores":      g.scores,
		"isGameOver":  g.isGameOver,
		"winner":      g.winner,
	}
}

func (g *DotsAndBoxesInstance) IsGameOver() bool {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.isGameOver
}

func (g *DotsAndBoxesInstance) GetWinner() interface{} {
	g.mu.Lock()
	defer g.mu.Unlock()
	return g.winner
}

func (g *DotsAndBoxesInstance) Cleanup() error {
	g.mu.Lock()
	defer g.mu.Unlock()

	g.lines = make(map[string]string)
	g.boxes = make(map[string]string)
	g.players = make(map[string]string)
	g.playerInfo = make(map[string]*PlayerInfo)
	g.scores = map[string]int{"1": 0, "2": 0}
	g.currentTurn = ""
	g.isGameOver = false
	g.winner = ""
	return nil
}

func (g *DotsAndBoxesInstance) buildPlayerNameMap() map[string]string {
	names := make(map[string]string)
	for _, info := range g.playerInfo {
		if info.State == PlayerStateActive {
			names[info.Symbol] = info.Username
		}
	}
	return names
}

func (g *DotsAndBoxesInstance) CalculateFinalResults(lobby_price float64, reason dto.GameEndReason, faultingPlayerID *string) []dto.PlayerResult {
	g.mu.Lock()
	defer g.mu.Unlock()

	if !g.isGameOver && reason != dto.GameEndReasonInsufficientPlayers {
		return nil
	}

	results := make([]dto.PlayerResult, 0, len(g.playerInfo))

	for playerID, info := range g.playerInfo {
		result := dto.PlayerResult{
			UserID:       playerID,
			UserName:     info.Username,
			SessionToken: info.SessionToken,
			Status:       dto.PlayerStatusUnknown,
			Metadata:     map[string]interface{}{
				"symbol": info.Symbol,
				"score":  g.scores[info.Symbol],
			},
		}

		if reason == dto.GameEndReasonInsufficientPlayers {
			result.Rank = 0
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Insufficient Players"}
		} else if info.State == PlayerStateLeft {
			result.Status = dto.PlayerStatusForfeit
			result.Rank = 2
			result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Forfeited game"}
		} else if g.winner != "" {
			if g.winner == info.Symbol {
				result.Status = dto.PlayerStatusWin
				result.Rank = 1
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 2 * lobby_price, Reason: "Winner"}
			} else {
				result.Status = dto.PlayerStatusLose
				result.Rank = 2
				result.CreditInfo = dto.PlayerCreditInfo{Amount: 0, Reason: "Lost"}
			}
		} else if g.isGameOver {
			// Draw case - both players have same score
			result.Status = dto.PlayerStatusDraw
			result.Rank = 1
			result.CreditInfo = dto.PlayerCreditInfo{Amount: lobby_price, Reason: "Draw"}
		}

		results = append(results, result)
	}

	return results
}
